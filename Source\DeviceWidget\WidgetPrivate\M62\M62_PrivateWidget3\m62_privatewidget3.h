#ifndef M62_PrivateWidget3_H
#define M62_PrivateWidget3_H

#include <QFont>
#include <QEvent>
#include <QObject>
#include <QResizeEvent>

#include "effectbase.h"
#include "workspace.h"
#include "appsettings.h"

namespace Ui {
class M62_PrivateWidget3;
}
class QLabel;
class M62_PrivateWidget3 : public EffectBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT

public:
    explicit M62_PrivateWidget3(QWidget* parent=nullptr, QString name="");
    ~M62_PrivateWidget3();

    void setReverbIcon(const QString& text);
    void setNCIcon(const QString& text);
    void setNCBypassIcon(bool enabled);
    void setMuteFxIcon(bool enabled);
    
    M62_PrivateWidget3& setName(QString name);
    M62_PrivateWidget3& setFont(QFont font);

    M62_PrivateWidget3& setValueInput1(int value);
    M62_PrivateWidget3& setValueInput2(int value);
    M62_PrivateWidget3& setValueAux(int value);
    M62_PrivateWidget3& setValueBluetooth(int value);
    M62_PrivateWidget3& setValueOtg(int value);
    M62_PrivateWidget3& setInput1Range(int min, int max);
    M62_PrivateWidget3& setInput2Range(int min, int max);
    M62_PrivateWidget3& setAuxRange(int min, int max);
    M62_PrivateWidget3& setBluetoothRange(int min, int max);
    M62_PrivateWidget3& setOtgRange(int min, int max);
    
    M62_PrivateWidget3& setLabelInput1Text(const QString& text);
    M62_PrivateWidget3& setLabelInput2Text(const QString& text);
    M62_PrivateWidget3& setLabelAuxText(const QString& text);
    M62_PrivateWidget3& setLabelBluetoothText(const QString& text);
    M62_PrivateWidget3& setLabelOtgText(const QString& text);
    
    QString getValueNoiseReductionKey(QString type);
    int getValueNoiseReduction(QString type);
    M62_PrivateWidget3& setValueNoiseReduction(int value);
    M62_PrivateWidget3& setValueNoiseReductionRange(float min, float max);
    M62_PrivateWidget3& setValueNoiseReductionType(QString type);
    M62_PrivateWidget3& updateNoiseReductionType(QString type);
    
    M62_PrivateWidget3& setReverbType(QString type);
    M62_PrivateWidget3& setValueDryWet(float value);
    M62_PrivateWidget3& setValueDryWetRange(float min, float max);
    M62_PrivateWidget3& setValueRoom(float value);
    M62_PrivateWidget3& setValueRoomRange(float min, float max);
    QString getValueReverbKey(QString type);
    int getValueReverb(QString type);
    M62_PrivateWidget3& updateReverbType(QString type);
    M62_PrivateWidget3& setValueDecay(float value);
    M62_PrivateWidget3& setValueDecayRange(float min, float max);
    
    M62_PrivateWidget3& setReverbTitle(const QString& title);
    M62_PrivateWidget3& setNoiseReductionTitle(const QString& title);
    
    M62_PrivateWidget3& setInputLevel1Text(const QString& text);
    M62_PrivateWidget3& setInputLevel2Text(const QString& text);
    M62_PrivateWidget3& setAuxLevelText(const QString& text);
    M62_PrivateWidget3& setBluetoothLevelText(const QString& text);
    M62_PrivateWidget3& setOtgLevelText(const QString& text);

    M62_PrivateWidget3& setLeftVolumeMeter(int value);
    M62_PrivateWidget3& setRightVolumeMeter(int value);
    M62_PrivateWidget3& setMUTEFX(bool enabled);
protected:
    void showEvent(QShowEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateStyle();
    void updateAttribute() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    QString getDbText(int value);
    bool eventFilter(QObject* watched, QEvent* event) override;
private:
    Ui::M62_PrivateWidget3* ui;
    QFont mFont;
    QString mReverbTypePre;
    QString mReverbType;
    int mReverbStudio=-2147483648;
    int mReverbLive=-2147483648;
    int mReverbHall=-2147483648;
    int mReverbBypass=-2147483648;
    QString mNoiseReductionTypePre;
    QString mNoiseReductionType;
    int mNoiseReductionNC1=-2147483648;
    int mNoiseReductionNC2=-2147483648;
    int mNoiseReductionNC3=-2147483648;
    int mNoiseReductionBypass=-2147483648;
    int mValueInput1=-2147483648;
    int mValueInput2=-2147483648;
    int mValueAux=-2147483648;
    int mValueBluetooth=-2147483648;
    int mValueOtg=-2147483648;
    float mPreDryWet=-2147483648;
    float mPreDecay=-2147483648;
    int mMuteFX=-2147483648;
    int mDefaulDbValueIN=0;
    int mDefaulDbValue=-90;
    void save(const QString& key, const QVariant& value);
private slots:
    void in_widgetDial_valueChanged(float value);
    void in_widgetDialDecay_valueChanged(float value);
    
    void in_sliderInput1_valueChanged(int value);
    void in_sliderInput2_valueChanged(int value);
    void in_sliderAux_valueChanged(int value);
    void in_sliderBluetooth_valueChanged(int value);
    void in_sliderOtg_valueChanged(int value);
};

#endif // M62_PrivateWidget3_H
