chcp 65001
setlocal enabledelayedexpansion
@echo off
cls

set "Deployer=D:\QT\6.9.0\msvc2022_64\bin\windeployqt.exe"
set "Packager=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
set "Signer=.\Extras\wosigncode.exe"
set "SignerTool=.\Extras\wosigncodecmd.exe"
set "SignerPasswordFile=.\..\..\..\USBKeyPassword.txt"
set "Compressor=D:\7ZIP\7-Zip\7z.exe"
set "Source=.\..\..\Build\Release"
set "Target=.\..\..\Build"

:CheckBuild
for %%f in ("%Source%\*.exe") do (
    set "Application=%%~nf"
    set "Source=!Source!\!Application!.exe"
    goto :CheckDriver
)
echo.
echo 未找到可执行文件
exit /b
:CheckDriver
for %%f in (".\Extras\UsbAudioDriverV*.exe") do (
    set "UsbAudioDriver=%%~nxf"
    set "UsbAudioDriverVersion=!UsbAudioDriver:UsbAudioDriverV=!"
    set "UsbAudioDriverVersion=!UsbAudioDriverVersion:.exe=!"
    goto :InputSeries
)
echo.
echo 未找到驱动程序
exit /b
:InputSeries
set /p Series="Series: "
if not exist ".\Series!Series!\Extras" (
    echo.
    echo "需要配置Series!Series!的打包环境"
    echo.
    goto :InputSeries
)
cls
:CheckVersion
set /p Version="Version: "
set "Version=V!Version!"
if exist "!Target!\!Version!" (
    echo.
    echo "!Version! 已存在"
    echo.
    goto :CheckVersion
)
for %%A in (%Version:.= %) do (
    set "Revision=%%A"
)
set "IsBeta=1"
set "BetaExtend= Beta"
if "!Revision!"=="0" (
    set "IsBeta=0"
    set "BetaExtend="
)
cls
set "SignPassword="
if not exist !SignerPasswordFile! (
    set /p SignPassword="签名密码(先插入密钥): "
) else (
    echo 已检测到密码文件,插入密钥然后任意键继续···
    pause | findstr /r /c:"$" >nul
    for /f "tokens=1,2 delims==" %%i in (!SignerPasswordFile!) do (
        if "%%i"=="USBKeyPassword" (
            set "SignPassword=%%j"
        )
    )
)
if not "!SignPassword!"=="" (
    powershell -Command "Start-Process '!Signer!'"
)
cls
echo 开始打包
mkdir "!Target!\!Version!\Deployment"
mkdir "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
copy "!Source!" "!Target!\!Version!\Deployment"
"!Deployer!" "!Target!\!Version!\Deployment\!Application!.exe"
copy ".\Extras\!UsbAudioDriver!" "!Target!\!Version!\Deployment"
copy ".\Extras\ToppingProLauncher.exe" "!Target!\!Version!\Deployment"
ren "!Target!\!Version!\Deployment\!UsbAudioDriver!" "UsbAudioDriver.exe"
"!Packager!" /DMyAppVersion=!Version! /DMyAppDriverVersion=!UsbAudioDriverVersion! /DMyAppIsBeta=!IsBeta! ".\Series!Series!\Script.iss"
if not "!SignPassword!"=="" (
    echo.
    echo.
    "!SignerTool!" sign /c /tp f82f61b31896b08be91e10c0239f94b9a737bbac /p !SignPassword! /dig sha256 /tr http://timestamp.digicert.com /file "!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.exe"
)
echo.
"!Compressor!" a -tzip "!Target!\!Version!\Deployment.zip" "!Target!\!Version!\Deployment\*"
"!Compressor!" a -tzip "!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.zip" "!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.exe"
move "!Target!\!Version!\Deployment.zip" "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
move "!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.zip" "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
"!Compressor!" a -tzip "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!.zip" "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!\*"
rmdir /s /q "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
powershell -Command "Start-Process '!Target!\!Version!\Deployment\!Application!.exe'"
powershell -Command "Start-Process '!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.exe'"
explorer "!Target!\!Version!"
echo.
echo.
echo 操作完成
echo.
endlocal
