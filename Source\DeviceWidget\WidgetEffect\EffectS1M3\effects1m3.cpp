#include "effects1m3.h"
#include "globalfont.h"
#include "ui_effects1m3.h"


EffectS1M3::EffectS1M3(QWidget* parent, QString name)
    : EffectBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::EffectS1M3)
{
    ui->setupUi(this);
    setAttribute(Qt::WA_StyledBackground, true);
    this->setObjectName(name);
    ui->ncWidget->setObjectName("ncWidget");
    ui->ncBypassWidget->setObjectName("ncBypassWidget");
    ui->lineEditNoiseReduction->setDisabled(true);
    ui->dialNoiseReduction->setRange(1, 70);
    auto setNCType = [this](QString text){
        ui->pushButtonBypassNoise->setChecked(false);
        if(text != "Bypass"){
            mNoiseReductionTypePre = text;
        }else{
            mNoiseReductionBypass = getValueNoiseReduction(mNoiseReductionTypePre);
            WorkspaceObserver::setValue(getValueNoiseReductionKey("Bypass"), mNoiseReductionBypass);
        }
        save("NoiseReductionType", text);
        updateNoiseReductionType(text);
        updateAttribute();
        setNCIcon(mNoiseReductionType);
    };
    connect(ui->pushButtonNC1, &QPushButton::clicked, this, [this, setNCType](bool checked) {
        setNCType("NC1");
    });
    connect(ui->pushButtonNC2, &QPushButton::clicked, this, [this, setNCType](bool checked) {
        setNCType("NC2");
    });
    connect(ui->pushButtonBypassNoise, &QPushButton::clicked, this, [this, setNCType](bool checked) {
        setNCType(checked?"Bypass":mNoiseReductionTypePre);
        ui->pushButtonBypassNoise->setChecked(checked);
    });
    connect(ui->dialNoiseReduction, &DialS1M5::valueChanged, this, [this](float value) {
        WorkspaceObserver::setValue(getValueNoiseReductionKey(mNoiseReductionType), value);
        QString key = "NoiseReduction";
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), "Save_" + key, QString::number(value));
        }
        updateAttribute();
    });
}
EffectS1M3::~EffectS1M3()
{
    delete ui;
}
EffectS1M3& EffectS1M3::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
EffectS1M3& EffectS1M3::setFont(QFont font)
{
    mFont = font;
    return *this;
}

void EffectS1M3::setNCBypassIcon(bool enabled){
    QString png;
    if(!enabled){
        png = ":/Image/PushButtonGroup/PBG3_0.png";
    }else{
        png = ":/Image/PushButtonGroup/PBG3_1.png";
    }
    ui->ncBypassWidget->setStyleSheet(QString("QWidget#ncBypassWidget{image:url(%1);}").arg(png));
}

void EffectS1M3::setNCIcon(const QString& text){
    QString png;
    if(text == "NC1"){
        png = ":/Image/PushButtonGroup/PBG5_2.png";
    }else if(text == "NC2"){
        png = ":/Image/PushButtonGroup/PBG5_3.png";
    }else if(text == "Bypass"){
        png = ":/Image/PushButtonGroup/PBG5_0.png";
    }
    setNCBypassIcon(text == "Bypass");
    ui->ncWidget->setStyleSheet(QString("QWidget#ncWidget{image:url(%1);}").arg(png));
}

QString EffectS1M3::getValueNoiseReductionKey(QString type)
{
    if(type == "NC1") {
        return "NoiseReductionNC1";
    } else if(type == "NC2") {
        return "NoiseReductionNC2";
    } else if(type == "Bypass") {
        return "NoiseReductionBypass";
    }
    return "";
}
int EffectS1M3::getValueNoiseReduction(QString type)
{
    auto key = getValueNoiseReductionKey(type);
    return WorkspaceObserver::value(key).toFloat();
}
EffectS1M3& EffectS1M3::setValueNoiseReduction(int value)
{
    ui->dialNoiseReduction->setValue(value);
    return *this;
}

EffectS1M3& EffectS1M3::setValueNoiseReductionType(QString type)
{
    if(type!="Bypass"){
        mNoiseReductionTypePre=type;
    }else{
        if(!mNoiseReductionTypePre.isEmpty()){
            mNoiseReductionBypass = getValueNoiseReduction(mNoiseReductionTypePre);
            WorkspaceObserver::setValue(getValueNoiseReductionKey("Bypass"), mNoiseReductionBypass);
        }
    }
    mNoiseReductionType = type;
    int value = getValueNoiseReduction(mNoiseReductionType);
    ui->pushButtonBypassNoise->setChecked(false);
    if(type == "NC1") {
        mNoiseReductionNC1 = value;
    } else if(type == "NC2") {
        mNoiseReductionNC2 = value;
    }else if(type == "Bypass") {
        ui->pushButtonBypassNoise->setChecked(true);
        mNoiseReductionBypass = value;
    }
    WorkspaceObserver::setValue("NoiseReductionType", mNoiseReductionType);
    updateNoiseReductionType(mNoiseReductionType);
    setNCIcon(mNoiseReductionType);
    return *this;
}

EffectS1M3& EffectS1M3::updateNoiseReductionType(QString type)
{
    ui->dialNoiseReduction->setEnabled(true);
    if(type == "Bypass") {
        ui->dialNoiseReduction->setEnabled(false);
    }
    ui->dialNoiseReduction->setValue(getValueNoiseReduction(type));
    return *this;
}

EffectS1M3& EffectS1M3::setNoiseReductionTitle(const QString& title) {
    ui->lineEditNoiseReduction->setText(title);
    return *this;
}


void EffectS1M3::resizeEvent(QResizeEvent* e) {
    Q_UNUSED(e);
    QRect widget2Rect = rect();
    int w2Width = widget2Rect.width();
    int w2Height = widget2Rect.height();
    ui->lineEditNoiseReduction->setGeometry(w2Width * 0, w2Height * 0, w2Width, w2Height * 0.08);
    ui->dialNoiseReduction->setGeometry((w2Width - w2Height * 0.19) / 2 ,w2Height * 0.12, w2Height * 0.19, w2Height * 0.19);
    ui->dialNoiseReduction->setFont(mFont);
    ui->labelNoiseClass->setGeometry(w2Width * 0.25, w2Height * 0.30, w2Width * 0.5, w2Height * 0.04);
    int h = w2Height * 0.19;
    int w = h * 1.2;
    ui->ncWidget->setGeometry((w2Width - w) / 2, w2Height * 0.45, w, h);
    h = w*35/60;
    ui->ncBypassWidget->setGeometry((w2Width - w) / 2, w2Height * 0.88, w, h);
    updateStyle();
}

void EffectS1M3::updateStyle(){
    auto setLabelFontSize = [=](QLabel* label, double factor = 1.0) {
        QFont font = mFont;
        font.setPointSize(GLBFHandle.getSuitablePointSize(mFont, label->text(), label->rect())*factor);
        label->setFont(font);
    };

    auto setTitleFontSize = [=](QWidget* widget) {
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, widget->height()) - 3);
        if(mFont.pointSize() < 8)
        {
            mFont.setPointSize(mFont.pointSize());
        }
        else if(mFont.pointSize() < 12)
        {
            mFont.setPointSize(mFont.pointSize() - 1);
        }
        else if(mFont.pointSize() < 17)
        {
            mFont.setPointSize(mFont.pointSize() - 2);
        }
        else if(mFont.pointSize() < 22)
        {
            mFont.setPointSize(mFont.pointSize() - 3);
        }
        widget->setFont(mFont);
    };       

    setTitleFontSize(ui->lineEditNoiseReduction);
    QFont font = mFont;
    font.setPointSize(GLBFHandle.getSuitablePixelSize(mFont, ui->labelNoiseClass->text(), ui->labelNoiseClass->rect())-2);
    ui->labelNoiseClass->setFont(font);

    QVector<QPushButton*> allButton={
        ui->pushButtonNC1,
        ui->pushButtonNC2,
        ui->pushButtonBypassNoise,
    };
    font.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->pushButtonBypassNoise->height()*0.8));
    for (auto button : allButton) {
        button->setFont(font);
    }

    QString style;
    style = QString("QWidget {"
            "   color: rgb(222, 222, 222);"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}"
            "QLabel{"
            "   background-color: transparent;"
            "}"
            "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-top-left-radius: %1px; border-top-right-radius: %1px;border-bottom-left-radius:0px; border-bottom-right-radius:0px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}").arg(width() * 0.04);
    style += QString("QPushButton {"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:checked {"
            "   color: rgb(229, 229, 229);"
            "}");

    setStyleSheet(style);
}

void EffectS1M3::updateAttribute()
{
    if(isWidgetReady())
    {
        if(mNoiseReductionType != WorkspaceObserver::value("NoiseReductionType").toString())
        {
            mNoiseReductionType = WorkspaceObserver::value("NoiseReductionType").toString();
            emit attributeChanged(this->objectName(), "NoiseReductionType", mNoiseReductionType);
            emit attributeChanged(this->objectName(), "NoiseReduction",  QString::number(getValueNoiseReduction(mNoiseReductionType)));
        }

        auto valueNoiseReduction = getValueNoiseReduction(mNoiseReductionType);
        if(mNoiseReductionType == "NC1" && valueNoiseReduction != mNoiseReductionNC1){
            mNoiseReductionNC1 = valueNoiseReduction;
            emit attributeChanged(this->objectName(), "NoiseReduction", QString::number(mNoiseReductionNC1));
        }
        else if(mNoiseReductionType == "NC2" && valueNoiseReduction != mNoiseReductionNC2) {
            mNoiseReductionNC2 = valueNoiseReduction;
            emit attributeChanged(this->objectName(), "NoiseReduction", QString::number(mNoiseReductionNC2));
        }
        else if(mNoiseReductionType == "Bypass" && valueNoiseReduction != mNoiseReductionBypass) {
            mNoiseReductionBypass = valueNoiseReduction;
            emit attributeChanged(this->objectName(), "NoiseReduction", QString::number(mNoiseReductionBypass));
        }
    }
}

void EffectS1M3::loadSettings()
{
    mNoiseReductionType = "";
    mNoiseReductionNC1 = -2147483648;
    mNoiseReductionNC2 = -2147483648;
    mNoiseReductionBypass = -2147483648;
    setWidgetReady(false);

    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag = WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();

    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("NoiseReductionNC1", 15);
        WorkspaceObserver::setValue("NoiseReductionNC2", 30);
        WorkspaceObserver::setValue("NoiseReductionBypass", 1);
        WorkspaceObserver::setValue("NoiseReductionType", "Bypass");
    }

    auto noiseReductionType = WorkspaceObserver::value("NoiseReductionType").toString();
    auto reverbType = WorkspaceObserver::value("ReverbType").toString();

    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_NoiseReduction", QString::number(getValueNoiseReduction(noiseReductionType)));
        emit attributeChanged(this->objectName(), "Save_NoiseReductionType", WorkspaceObserver::value("NoiseReductionType").toString());
    }

    setWidgetReady(true);
    updateAttribute();
    setValueNoiseReductionType(noiseReductionType);
    if(noiseReductionType == "Bypass"){
        noiseReductionType = "NC1";
    }
    mNoiseReductionTypePre = noiseReductionType;
}

void EffectS1M3::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    if (attribute == "Language") {
        if(value == "English") {
            setNoiseReductionTitle("Noise Reduction");
            ui->labelNoiseClass->setText("Level");
        }
        else if (value == "Chinese") {
            setNoiseReductionTitle("降噪");
            ui->labelNoiseClass->setText("降噪量");
        }
    }
}

void EffectS1M3::save(const QString& key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key, value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}