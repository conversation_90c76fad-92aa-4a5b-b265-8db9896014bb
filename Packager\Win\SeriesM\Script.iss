; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#ifndef MyAppVersion
#define MyAppVersion "V1.0.0"
#endif
#ifndef MyAppDriverVersion
#define MyAppDriverVersion "1.0.0"
#endif
#ifndef MyAppIsBeta
#define MyAppIsBeta "0"
#endif

#define MyAppName "M Control Center"
#define MyAppExeName "M Control Center.exe"
#define MyAppId "{20D781DE-E03A-45BA-89A5-1CC8C46D2BD8}"
#define MyAppPackagePath ".\..\..\..\Build"
#define MyAppInstallPath "C:\Program Files\TOPPING Pro"
#define MyAppPublisher "TOPPING"
#define MyAppURL "https://www.topping.pro/"
#define MyAppDriver "UsbAudioDriver.exe"
#define MyAppDriverForceUpgrade "0"

#if MyAppIsBeta == "1"
#define MyAppVersionExtend " Beta"
#else
#define MyAppVersionExtend ""
#endif

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{#MyAppId}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
;AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={#MyAppInstallPath}\{#MyAppName}
Uninstallable=yes
UninstallDisplayName={#MyAppName}
UninstallDisplayIcon={app}\{#MyAppExeName}
; "ArchitecturesAllowed=x64compatible" specifies that Setup cannot run
; on anything but x64 and Windows 11 on Arm.
ArchitecturesAllowed=x64compatible
; "ArchitecturesInstallIn64BitMode=x64compatible" requests that the
; install be done in "64-bit mode" on x64 or Windows 11 on Arm,
; meaning it should use the native 64-bit Program Files directory and
; the 64-bit view of the registry.
ArchitecturesInstallIn64BitMode=x64compatible
DisableProgramGroupPage=yes
LicenseFile=.\Extras\License.rtf
; Uncomment the following line to run in non administrative install mode (install for current user only).
;PrivilegesRequired=lowest
OutputDir={#MyAppPackagePath}\{#MyAppVersion}
OutputBaseFilename={#MyAppName} {#MyAppVersion}{#MyAppVersionExtend} Setup
SetupIconFile=.\Extras\AppIcon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
WizardImageFile=.\Extras\Setup.bmp

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "chinesesimplified"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "{#MyAppPackagePath}\{#MyAppVersion}\Deployment\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "{#MyAppPackagePath}\{#MyAppVersion}\Deployment\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "{#MyAppPackagePath}\{#MyAppVersion}\Deployment\{#MyAppDriver}"; DestDir: "{app}"; Flags: ignoreversion  ; AfterInstall: RunOtherInstaller
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[Dirs]
Name: {app}; Permissions: users-full   


; ProcedureExtend
[CODE]
var
  CloseDevicePage: TWizardPage;
  CloseDeviceLabel: TLabel;
  CloseDeviceCheckBox: TNewCheckBox;
  KillErrorCode: Integer;
  LanguageInit: String;
  CloseDevicePageTitle: String;
  CloseDevicePageTitleLable: String;
  CloseDevicePageLable: String;
  CloseDevicePageCheck: String;
  CloseDevicePageLable2: String;
  NextButton: TButton;

function FindToppingProPublisher: string;
var
  RootKey: Cardinal;
  SubkeyNames: TArrayOfString;
  // RegPath: string;
  Subkey: string;
  Publisher: string;
  I: Integer;
begin
  Result := '';
    // 设置注册表视图为 64 位
  RootKey := HKLM64 ;   
  if RegGetSubkeyNames(RootKey,'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall' , SubkeyNames) then
  begin
    for I := 0 to GetArrayLength(SubkeyNames) - 1 do
    begin
      Subkey := SubkeyNames[I];
      if RegQueryStringValue(RootKey, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\' + Subkey, 'Publisher', Publisher) then
      begin
        if CompareText(Publisher, 'TOPPING Pro') = 0 then
        begin
          Result := Subkey;
          Break; // Found the desired Publisher, no need to continue
        end;
      end;
    end;
  end;
end;


function GetDisplayVersionForSubkey(SubkeyName: string): string;
var
  RegKey: string;
  DisplayVersion: string;
begin
  Result := '';
  RegKey := 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\' + SubkeyName;               // 拼接完整的注册表路径
  if RegQueryStringValue(HKLM64, RegKey, 'DisplayVersion', DisplayVersion) then     // 查询 DisplayVersion 值
  begin
    Result := DisplayVersion;
  end;
end;

function GetInstallLocationForSubkey(SubkeyName: string): string;
var
  RegKey: string;
  InstallLocation: string;
begin
  Result := '';
  RegKey :=  'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\'+ SubkeyName;             // 拼接完整的注册表路径
  if RegQueryStringValue(HKLM64, RegKey, 'InstallLocation', InstallLocation) then           // 查询 InstallLocation 值
  begin
    Result := InstallLocation;
  end;
end;





procedure ReplaceString(var Source: String; const Find, Replace: String);
var
  StartPos: Integer;
begin
  StartPos := Pos(Find, Source);
  while StartPos > 0 do
  begin
    Delete(Source, StartPos, Length(Find));
    Insert(Replace, Source, StartPos);
    StartPos := Pos(Find, Source);
  end;
end;

procedure WriteJSONStringToFile(AudioDveicePath: string; FilePath: string);
var
  JSONContent: string;

begin
       // 自定义 JSON 字符串
 ReplaceString(AudioDveicePath, '\', '/');
JSONContent :=
  '{' + #13#10 +
  '  "Launcher": "' + AudioDveicePath + '"' + #13#10 +
  '}';

  // 将 JSON 字符串写入文件
  if not SaveStringToFile(FilePath, JSONContent, False) then
  begin
    //MsgBox('Failed to write the JSON string to the file.', mbError, MB_OK);
  end;
end;

procedure MoveFileAndDeleteSource(SourceFileName, TargetFileName: String);
begin
  if not CopyFile(SourceFileName, TargetFileName, False) then
    begin
      //MsgBox('Failed to move the file.', mbError, MB_OK);
      WizardForm.Close;
    end
  else
    begin
        if not DeleteFile(SourceFileName) then
        begin
         //MsgBox('Failed to delete the source file.', mbError, MB_OK);
          WizardForm.Close;
        end;
    end;
end;



//触发时机：安装过程中当前步骤发生变化被调用。会在用户
procedure CurStepChanged(CurStep: TSetupStep);
var
  InstallPath: string;
  uninspath, uninsname, NewUninsName: string;
  MyCustomAppName: string;
  MyCustomAppId: string;
  ToppingProUninstallKey: string;
  Location: string;
  MoveSourceFile: String;
  MoveTargetDir: String;

begin
 //用于检查当前的安装步骤是否是 "PostInstall"（安装后）
  if CurStep = ssPostInstall then
  begin
    //获取软件路径
    InstallPath := ExpandConstant('{app}');
    //MsgBox('当前安装路径为: ' + InstallPath, mbInformation, MB_OK);

    //获取驱动路径
    ToppingProUninstallKey := FindToppingProPublisher;
    if ToppingProUninstallKey <> '' then
    begin
        Location :=GetInstallLocationForSubkey(ToppingProUninstallKey);
        if  Length(Location) = 0 then
        begin
           // MsgBox('参数 Location为空  旧版本过旧' , mbInformation, MB_OK);
        end
        else
        begin
           // MsgBox('参数 Location : ' + Location, mbInformation, MB_OK);
        end;
    end
    else
    begin
       // MsgBox('Abnormal installation driver', mbError, MB_OK);       
    end;

    //MsgBox('生成一个json文件' , mbInformation, MB_OK);
     //生成一个json文件   内容：软件路径  写入：驱动路径
     WriteJSONStringToFile(InstallPath, Location+'MyJSONFile.json');
     //MsgBox('生成完成' , mbInformation, MB_OK);

     // 定义源文件和目标目录
     MoveSourceFile := ExpandConstant('{app}\ToppingProLauncher.exe');
     MoveTargetDir := Location+'x64'; // Replace with your desired target directory
     // 检查目标目录是否存在，如果不存在则创建它
     if not DirExists(MoveTargetDir) then
       if not ForceDirectories(MoveTargetDir) then
       begin
         //MsgBox('Failed to create the target directory.', mbError, MB_OK);
         //WizardForm.Close;         //关闭 Inno Setup 的安装向导窗口
       end;
      // Move the file to the target directory and delete the source file
      MoveFileAndDeleteSource(MoveSourceFile, MoveTargetDir + '\ToppingProLauncher.exe');
  end;

    if CurStep=ssDone then
      begin
      // 指定新的卸载文件名（不包含扩展名），请相应修改！
      NewUninsName := 'uninstall_{#MyAppName}';    
      // 应用程序名称，与 [SEUTP] 段的 AppName 必须一致，请相应修改！
      MyCustomAppName := '{#MyAppName}'; 
      MyCustomAppId:= '{#MyAppId}' ;

      // 以下重命名卸载文件
      uninspath:= ExtractFilePath(ExpandConstant('{uninstallexe}'));
      uninsname:= Copy(ExtractFileName(ExpandConstant('{uninstallexe}')),1,8);
      //删除旧版本的同名卸载文件（如果存在）  
      DeleteFile(ExpandConstant(uninspath + NewUninsName + '.exe'));
      DeleteFile(ExpandConstant(uninspath + NewUninsName + '.dat'));

      RenameFile(uninspath + uninsname + '.exe', uninspath + NewUninsName + '.exe');
      RenameFile(uninspath + uninsname + '.dat', uninspath + NewUninsName + '.dat');
      // 以下修改相应的注册表内容
        if RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppName + '_is1') then
          begin
              RegWriteStringValue(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppName + '_is1', 'UninstallString', '"' + uninspath + NewUninsName + '.exe"');
              RegWriteStringValue(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppName + '_is1', 'QuietUninstallString', '"' + uninspath + NewUninsName + '.exe" /SILENT');
          end;
        if RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppName + '_is1') then
          begin
              RegWriteStringValue(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppName + '_is1', 'UninstallString', '"' + uninspath + NewUninsName + '.exe"');
              RegWriteStringValue(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppName + '_is1', 'QuietUninstallString', '"' + uninspath + NewUninsName + '.exe" /SILENT');
          end;   


        if RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppId + '_is1') then
          begin
              RegWriteStringValue(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppId + '_is1', 'UninstallString', '"' + uninspath + NewUninsName + '.exe"');
              RegWriteStringValue(HKEY_LOCAL_MACHINE, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppId + '_is1', 'QuietUninstallString', '"' + uninspath + NewUninsName + '.exe" /SILENT');
          end;
        if RegKeyExists(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppId + '_is1') then
          begin
              RegWriteStringValue(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppId + '_is1', 'UninstallString', '"' + uninspath + NewUninsName + '.exe"');
              RegWriteStringValue(HKEY_LOCAL_MACHINE, 'SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\' + MyCustomAppId + '_is1', 'QuietUninstallString', '"' + uninspath + NewUninsName + '.exe" /SILENT');
          end;
      end;

end;

procedure CloseDeviceCheckBoxClick(Sender: TObject);
begin
  // 当CloseDeviceCheckBox被选中时，启用下一步按钮
  NextButton.Enabled := CloseDeviceCheckBox.Checked;
end;
//触发时机：InitializeWizard 函数在安装向导初始化时被调用，通常是在用户启动安装程序后、但在显示任何安装向导页面之前。
//目的：这个函数用于配置和初始化安装向导的外观和行为，例如设置标题、按钮文本、页面布局，初始化变量，执行前期操作等。它通常用于处理与安装过程开始前相关的设置和准备工作。
procedure InitializeWizard();
begin
  WizardForm.LICENSEACCEPTEDRADIO.Checked:=false;

  LanguageInit := ExpandConstant('{language}');
  if LanguageInit = 'chinesesimplified' then
    begin
    CloseDevicePageTitle := '断开音频接口' ;
    CloseDevicePageTitleLable := '请先断开音频接口，然后再继续。';
    CloseDevicePageLable  := '在继续安装之前，请确保断开音频接口。' ;
    CloseDevicePageCheck := '我已断开音频接口。' ;
    CloseDevicePageLable2 := '请确认您已断开音频接口。' ;
    end
  else
    begin
    CloseDevicePageTitle := 'Disconnect the audio interface';
    CloseDevicePageTitleLable := 'Please disconnect the audio interface before continuing.';
    CloseDevicePageLable  := 'Make sure to disconnect the audio interface before continuing with the installation.' ;
    CloseDevicePageCheck := 'I have disconnected the audio interface.' ;
    CloseDevicePageLable2 := 'Please confirm that you have disconnected the audio interface.' ;
    end ;
    // Create a custom page to prompt the user to close the device
  CloseDevicePage := CreateCustomPage(wpWelcome, CloseDevicePageTitle, CloseDevicePageTitleLable);

  // Create a label with instructions
  CloseDeviceLabel := TLabel.Create(WizardForm);
  CloseDeviceLabel.Parent := CloseDevicePage.Surface;
  CloseDeviceLabel.Left := ScaleX(16);
  CloseDeviceLabel.Top := ScaleY(16);
  CloseDeviceLabel.Width := CloseDevicePage.SurfaceWidth - ScaleX(32);
  CloseDeviceLabel.Caption := CloseDevicePageLable;

  // Create a checkbox to confirm the device is closed
  CloseDeviceCheckBox := TNewCheckBox.Create(WizardForm);
  CloseDeviceCheckBox.Parent := CloseDevicePage.Surface;
  CloseDeviceCheckBox.Left := ScaleX(16);
  CloseDeviceCheckBox.Top := CloseDeviceLabel.Top + CloseDeviceLabel.Height + ScaleY(8);
  CloseDeviceCheckBox.Width := CloseDevicePage.SurfaceWidth - ScaleX(32);
  CloseDeviceCheckBox.Caption := CloseDevicePageCheck;
  CloseDeviceCheckBox.Checked := False;

  WizardForm.NextButton.Enabled:= False;                 // 初始状态下禁用下一步按钮
  // 创建下一步按钮
  NextButton := WizardForm.NextButton;

    // 监听CloseDeviceCheckBox的状态变化事件
  CloseDeviceCheckBox.OnClick := @CloseDeviceCheckBoxClick;
end;

procedure CurPageChanged(CurPageID: Integer);
begin
  if CurPageID = CloseDevicePage.ID then
  begin
    // Access the "Next" button
    WizardForm.NextButton.Enabled := CloseDeviceCheckBox.Checked;
  end;
   if CurPageID = wpLicense then
  begin
    if  CloseDeviceCheckBox.Checked then
    begin
      ShellExec('open', ExpandConstant('{cmd}'), '/c taskkill /f /t /im {#MyAppExeName}', '', SW_HIDE, ewNoWait, KillErrorCode);     
    end;     
  end;
end;


 //执行驱动安装的步骤
procedure RunOtherInstaller;
var
  ToppingProUninstallKey: string;
  SystemDriveVersion: string;            //读取系统的
  UpdateDriveVersion: string;             //当前更新的
  ExecResultCode: Integer;
  ShouldInstallNewerOrEqual: Boolean;
  ShouldInstall: Boolean;

begin
    // 使用 FindToppingProPublisher 函数查找 ToppingPro 的 Publisher
    ToppingProUninstallKey := FindToppingProPublisher;
    if ToppingProUninstallKey <> '' then
    begin
      SystemDriveVersion := GetDisplayVersionForSubkey(ToppingProUninstallKey);
      //MsgBox('参数 DisplayVersion : ' + SystemDriveVersion, mbInformation, MB_OK);
      UpdateDriveVersion :=  '{#MyAppDriverVersion}';
      // 读取预定义的值并转换为布尔值
      ShouldInstallNewerOrEqual := (LowerCase('{#MyAppDriverForceUpgrade}') = '1');

      if ShouldInstallNewerOrEqual then
          begin
            ShouldInstall := (CompareStr(UpdateDriveVersion, SystemDriveVersion) >= 0);
          end
      else
          begin
            ShouldInstall := (CompareStr(UpdateDriveVersion, SystemDriveVersion) > 0);
          end;

      if ShouldInstall  then
          begin
            //MsgBox('需要执行安装下载新的驱动 '+UpdateDriveVersion , mbInformation, MB_OK);
            if  not  Exec(ExpandConstant('{app}\{#MyAppDriver}'), '', '', SW_SHOWNORMAL, ewWaitUntilTerminated, ExecResultCode) then             //ewWaitUntilTerminated         ewNoWait
            begin
                MsgBox('无法安装驱动: ' + SysErrorMessage(ExecResultCode), mbError, MB_OK);          
            end;         
          end
      else
          begin
            //MsgBox(' 当前电脑安装驱动版本为最新： '+ UpdateDriveVersion , mbInformation, MB_OK);    
          end;
    end
    else
    begin
       //MsgBox('未安装驱动异常：'+ ToppingProUninstallKey, mbInformation, MB_OK);                     // ToppingPro 未在注册表中找到     
       // 进行其他操作...      首次安装
       if  not  Exec(ExpandConstant('{app}\{#MyAppDriver}'), '', '', SW_SHOWNORMAL, ewWaitUntilTerminated, ExecResultCode) then             //ewWaitUntilTerminated         ewNoWait
              begin
              //MsgBox('无法安装驱动: ' + SysErrorMessage(ExecResultCode), mbError, MB_OK);          
              end;                                          
    end;
end;

