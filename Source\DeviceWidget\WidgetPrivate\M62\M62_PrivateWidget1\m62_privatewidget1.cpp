#include "m62_privatewidget1.h"
#include "ui_m62_privatewidget1.h"
#include <QPainter>
#include <QSvgRenderer>
#include "globalfont.h"
#include <float.h>

M62_PrivateWidget1::M62_PrivateWidget1(QWidget *parent, const QString& name)
    : FramelessWindow(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::M62_PrivateWidget1)
    , mWidget(new QWidget)
{
    ui->setupUi(mWidget);
    setWindowFlags(windowFlags() | Qt::SplashScreen);
    setWindowMode(FramelessWindow::WindowMode::Custom);
    setShadowRadius(5);
    setRightBottomDraggable();
    setCentralWidget(mWidget);
    initConnect();
    ui->horizontalLayout->setAlignment(Qt::AlignTop);

    ui->widgetDials->setObjectName("widgetDials");
    ui->DialAttack->setColorBG(Qt::transparent);
    ui->DialAttack->showCircle(false);
    ui->DialReduction->setColorBG(Qt::transparent);
    ui->DialReduction->showCircle(false);
    ui->DialRelease->setColorBG(Qt::transparent);
    ui->DialRelease->showCircle(false);
    ui->DialThreshold->setColorBG(Qt::transparent);
    ui->DialThreshold->showCircle(false);

    ui->labelTitle->setStyleSheet("color:rgb(222, 222, 222);");
    ui->buttonClose->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/close.svg); }");
    ui->buttonSwitch->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/autoStartUnchecked.svg); }QPushButton:checked { image: url(:/Icon/autoStart.svg); }");
    ui->widgetDockingMap->setStyleSheet("QWidget{background:rgb(22, 22, 22);border-radius:10px;color:rgb(222, 222, 222)}");
    ui->widget->setStyleSheet("background:transparent;color:rgba(222, 222, 222, 1)");
    ui->labelTitle_2->setStyleSheet(QString("background:rgb(22, 22, 22);"
            "   border-top-left-radius: 10px;"
            "   border-top-right-radius: 10px;"
            "   border-bottom-left-radius: 0px;"
            "   border-bottom-right-radius: 0px;"
            "   border-bottom: 1px solid rgb(31,31,31);"
            "   color:rgb(255, 255, 255);"));
    ui->widgetDials->setStyleSheet("QWidget{background:rgb(22, 22, 22);border-radius:10px;color:rgb(170,170,170)}");
    ui->widgeReduction->setStyleSheet("background:transparent");
    ui->widgetlAttack->setStyleSheet("background:transparent");
    ui->widgetRelease->setStyleSheet("background:transparent");
    ui->widgetThreshold->setStyleSheet("background:transparent");
    ui->DialThresholdT->setStyleSheet("color:rgba(222, 222, 222, 1)");
    ui->DialAttackT->setStyleSheet("color:rgba(222, 222, 222, 1)");
    ui->DialReductionT->setStyleSheet("color:rgba(222, 222, 222, 1)");
    ui->DialReleaseT->setStyleSheet("color:rgba(222, 222, 222, 1)");

    reset();
    ui->DialThreshold->setStep(1);
    ui->DialAttack->setStep(50);
    ui->DialAttack->setStepWheel(10);
    ui->DialReduction->setStep(1);
    ui->DialRelease->setStep(50);
    ui->DialRelease->setStepWheel(10);
#ifdef Q_OS_MACOS
    ui->DialThreshold->setPlaceText("+888888");
    ui->DialAttack->setPlaceText("+888888");
    ui->DialReduction->setPlaceText("+888888");
    ui->DialRelease->setPlaceText("+888888");
#endif
    setThresholdRange(-60, -1);
    setThresholdDefaultValue(-30);
    setThresholdLabelL("-60dB");
    setThresholdLabelR("-1dB");
    setAttackRange(0, 1000);
    setAttackDefaultValue(200);
    setAttackLabelL("0ms");
    setAttackLabelR("1000ms");
    setReductionRange(1, 30);
    setReductionDefaultValue(16);
    setReductionLabelL("1dB");
    setReductionLabelR("30dB");
    setReleaseRange(10, 2000);
    setReleaseDefaultValue(1000);
    setReleaseLabelL("10ms");
    setReleaseLabelR("2000ms");

    ui->widgetDials->installEventFilter(this);
}

M62_PrivateWidget1::~M62_PrivateWidget1()
{
    delete ui;
}

void M62_PrivateWidget1::setName(const QString &name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
}

void M62_PrivateWidget1::setScaleFactor(double sizeFactor)
{
    int shadowRadius = 2*getConfig().shadowRadius;
    static QSize baseSize(521+shadowRadius,406+shadowRadius);
    mSizeFactor = sizeFactor;
    setMinimumSize(baseSize.width() * sizeFactor, baseSize.height() * sizeFactor);
    resize(minimumSize().width(), minimumSize().height());
}

void M62_PrivateWidget1::setFont(const QFont &font)
{
    FramelessWindow::setFont(font);
    setAllChildFont(this, font);
    ui->input->setFont(font);
}

void M62_PrivateWidget1::setINName(const QString &str)
{
    ui->input->setName(str);
}

void M62_PrivateWidget1::setINChannelName(const QString &str)
{
    ui->input->setChannelName(str,false);
}

void M62_PrivateWidget1::setVolumeMeterLeft(int value)
{
    ui->input->setVolumeMeterLeft(value);
}

void M62_PrivateWidget1::setVolumeMeterRight(int value)
{
    ui->input->setVolumeMeterRight(value);
}

float M62_PrivateWidget1::getThresholdDefaultValue()
{
    return ui->DialThreshold->getDefault();
}

void M62_PrivateWidget1::setThresholdDefaultValue(float value)
{
    ui->DialThreshold->setDefault(value);
}

void M62_PrivateWidget1::setThresholdValue(float value, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->DialThreshold);
        ui->DialThreshold->setValue(value);
    }else{
        ui->DialThreshold->setValue(value);
    }
}

void M62_PrivateWidget1::setThresholdRange(float min, float max)
{
    ui->DialThreshold->setRange(min, max);
}

void M62_PrivateWidget1::setThresholdLabelL(const QString &text)
{
    ui->DialThresholdL->setText(text);
}

void M62_PrivateWidget1::setThresholdLabelR(const QString &text)
{
    ui->DialThresholdR->setText(text);
}

float M62_PrivateWidget1::getAttackDefaultValue()
{
    return ui->DialAttack->getDefault();
}

void M62_PrivateWidget1::setAttackDefaultValue(float value)
{
    ui->DialAttack->setDefault(value);
}

void M62_PrivateWidget1::setAttackValue(float value, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->DialAttack);
        ui->DialAttack->setValue(value);
    }else{
        ui->DialAttack->setValue(value);
    }
}

void M62_PrivateWidget1::setAttackRange(float min, float max)
{
    ui->DialAttack->setRange(min, max);
}

void M62_PrivateWidget1::setAttackLabelL(const QString &text)
{
    ui->DialAttackL->setText(text);
}

void M62_PrivateWidget1::setAttackLabelR(const QString &text)
{
    ui->DialAttackR->setText(text);
}

float M62_PrivateWidget1::getReductionDefaultValue()
{
    return ui->DialReduction->getDefault();
}

void M62_PrivateWidget1::setReductionDefaultValue(float value)
{
    ui->DialReduction->setDefault(value);
}

void M62_PrivateWidget1::setReductionValue(float value, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->DialReduction);
        ui->DialReduction->setValue(value);
    }else{
        ui->DialReduction->setValue(value);
    }
}

void M62_PrivateWidget1::setReductionRange(float min, float max)
{
    ui->DialReduction->setRange(min, max);
}

void M62_PrivateWidget1::setReductionLabelL(const QString &text)
{
    ui->DialReductionL->setText(text);
}

void M62_PrivateWidget1::setReductionLabelR(const QString &text)
{
    ui->DialReductionR->setText(text);
}

float M62_PrivateWidget1::getReleaseDefaultValue()
{
    return ui->DialRelease->getDefault();
}

void M62_PrivateWidget1::setReleaseDefaultValue(float value)
{
    ui->DialRelease->setDefault(value);
}

void M62_PrivateWidget1::setReleaseValue(float value, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->DialRelease);
        ui->DialRelease->setValue(value);
    }else{
        ui->DialRelease->setValue(value);
    }
}

void M62_PrivateWidget1::setReleaseRange(float min, float max)
{
    ui->DialRelease->setRange(min, max);
}

void M62_PrivateWidget1::setReleaseLabelL(const QString &text)
{
    ui->DialReleaseL->setText(text);
}

void M62_PrivateWidget1::setReleaseLabelR(const QString &text)
{
    ui->DialReleaseR->setText(text);
}

void M62_PrivateWidget1::setButtonOFFChecked(bool isChecked, bool isSendSig, bool isSendWorkState)
{
    const QSignalBlocker blocker(ui->buttonSwitch);
    ui->buttonSwitch->setChecked(isChecked);
    if(isSendSig){
        on_buttonOFF_toggled(isChecked);
    }
    mOFF = isChecked;
    WorkspaceObserver::setValue("DuckingSwitch", isChecked?"true":"false");
    saveAttribute("DuckingSwitch", QString::number(isChecked));
    if(isSendWorkState){
        sendSigWorkState(1);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setIN1AUXChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN1AUX);
        ui->IN1AUX->setChecked(isChecked);
    }else{
        ui->IN1AUX->setChecked(isChecked);
        sendSigWorkState(1);
    }
}

void M62_PrivateWidget1::setIN2AUXChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN2AUX);
        ui->IN2AUX->setChecked(isChecked);
    }else{
        ui->IN2AUX->setChecked(isChecked);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setIN1BTChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN1BT);
        ui->IN1BT->setChecked(isChecked);
    }else{
        ui->IN1BT->setChecked(isChecked);
        sendSigWorkState(1);
    }
}

void M62_PrivateWidget1::setIN2BTChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN2BT);
        ui->IN2BT->setChecked(isChecked);
    }else{
        ui->IN2BT->setChecked(isChecked);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setIN1OTGChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN1OTG);
        ui->IN1OTG->setChecked(isChecked);
    }else{
        ui->IN1OTG->setChecked(isChecked);
        sendSigWorkState(1);
    }
}

void M62_PrivateWidget1::setIN2OTGChecked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->IN2OTG);
        ui->IN2OTG->setChecked(isChecked);
    }else{
        ui->IN2OTG->setChecked(isChecked);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setplayback1_2IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback1_2IN1);
        ui->playback1_2IN1->setChecked(isChecked);
    }else{
        ui->playback1_2IN1->setChecked(isChecked);
        sendSigWorkState(1);
    }
}

void M62_PrivateWidget1::setplayback1_2IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback1_2IN2);
        ui->playback1_2IN2->setChecked(isChecked);
    }else{
        ui->playback1_2IN2->setChecked(isChecked);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setplayback3_4IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback3_4IN1);
        ui->playback3_4IN1->setChecked(isChecked);
    }else{
        ui->playback3_4IN1->setChecked(isChecked);
        sendSigWorkState(1);
    }
}

void M62_PrivateWidget1::setplayback3_4IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback3_4IN2);
        ui->playback3_4IN2->setChecked(isChecked);
    }else{
        ui->playback3_4IN2->setChecked(isChecked);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setplayback5_6IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback5_6IN1);
        ui->playback5_6IN1->setChecked(isChecked);
    }else{
        ui->playback5_6IN1->setChecked(isChecked);
        sendSigWorkState(1);
    }
}

void M62_PrivateWidget1::setplayback5_6IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback5_6IN2);
        ui->playback5_6IN2->setChecked(isChecked);
    }else{
        ui->playback5_6IN2->setChecked(isChecked);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setplayback7_8IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback7_8IN1);
        ui->playback7_8IN1->setChecked(isChecked);
    }else{
        ui->playback7_8IN1->setChecked(isChecked);
        sendSigWorkState(1);
    }
}

void M62_PrivateWidget1::setplayback7_8IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback7_8IN2);
        ui->playback7_8IN2->setChecked(isChecked);
    }else{
        ui->playback7_8IN2->setChecked(isChecked);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setplayback9_10IN1Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback9_10IN1);
        ui->playback9_10IN1->setChecked(isChecked);
    }else{
        ui->playback9_10IN1->setChecked(isChecked);
        sendSigWorkState(1);
    }
}

void M62_PrivateWidget1::setplayback9_10IN2Checked(bool isChecked, bool isSendSig)
{
    if(!isSendSig){
        const QSignalBlocker blocker(ui->playback9_10IN2);
        ui->playback9_10IN2->setChecked(isChecked);
    }else{
        ui->playback9_10IN2->setChecked(isChecked);
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::setPlayback3_4BackIsVisible(bool isVisible)
{
    ui->playback3_4IN1->setVisible(isVisible);
    ui->playback3_4IN2->setVisible(isVisible);
    ui->labelpb34->setVisible(isVisible);
    ui->playback5_6IN1->setVisible(isVisible);
    ui->playback5_6IN2->setVisible(isVisible);
    ui->labelpb56->setVisible(isVisible);
    ui->playback7_8IN1->setVisible(isVisible);
    ui->playback7_8IN2->setVisible(isVisible);
    ui->labelpb78->setVisible(isVisible);
    ui->playback9_10IN1->setVisible(isVisible);
    ui->playback9_10IN2->setVisible(isVisible);
    ui->labelpb910->setVisible(isVisible);
}

void M62_PrivateWidget1::setSizeFactor(double sizeFactor)
{
    int topWidgetHeight = 0.11*mWidget->height();
    int spacing = 0.0161*mWidget->width();
    ui->buttonSwitch->setFixedSize(topWidgetHeight * 0.4, topWidgetHeight * 0.4);
    ui->buttonClose->setFixedSize(topWidgetHeight * 0.35, topWidgetHeight * 0.35);
    ui->widgetTop->layout()->setContentsMargins(spacing, 0.009 * mWidget->width(), spacing, 0);
    ui->labelTitle->setFixedHeight(topWidgetHeight * 0.5);

    setDialGeometry(ui->DialAttack, ui->DialAttackT, ui->DialAttackL, ui->DialAttackR);
    setDialGeometry(ui->DialReduction, ui->DialReductionT, ui->DialReductionL, ui->DialReductionR);
    setDialGeometry(ui->DialRelease, ui->DialReleaseT, ui->DialReleaseL, ui->DialReleaseR);
    setDialGeometry(ui->DialThreshold, ui->DialThresholdT, ui->DialThresholdL, ui->DialThresholdR);

    ui->gridLayout_3->setHorizontalSpacing(0.02*ui->widgetDials->width());
    ui->gridLayout_3->setVerticalSpacing(0.025*ui->widgetDials->height());
    int diameter = 0.1*ui->widgetDials->width();
    ui->IN1AUX->setFixedSize(diameter, diameter);
    ui->IN2AUX->setFixedSize(diameter, diameter);
    ui->IN1BT->setFixedSize(diameter, diameter);
    ui->IN2BT->setFixedSize(diameter, diameter);
    ui->IN1OTG->setFixedSize(diameter, diameter);
    ui->IN2OTG->setFixedSize(diameter, diameter);
    ui->playback1_2IN1->setFixedSize(diameter, diameter);
    ui->playback1_2IN2->setFixedSize(diameter, diameter);
    ui->playback3_4IN1->setFixedSize(diameter, diameter);
    ui->playback3_4IN2->setFixedSize(diameter, diameter);
    ui->playback5_6IN1->setFixedSize(diameter, diameter);
    ui->playback5_6IN2->setFixedSize(diameter, diameter);
    ui->playback7_8IN1->setFixedSize(diameter, diameter);
    ui->playback7_8IN2->setFixedSize(diameter, diameter);
    ui->playback9_10IN1->setFixedSize(diameter, diameter);
    ui->playback9_10IN2->setFixedSize(diameter, diameter);

    QFont font = ui->labelTitle->font();
    font.setPointSize(GLBFHandle.getSuitablePointSize(font, ui->labelTitle->height()*0.8));
    ui->labelTitle->setFont(font);
    ui->DialAttack->setFont(font);
    ui->DialReduction->setFont(font);
    ui->DialRelease->setFont(font);
    ui->DialThreshold->setFont(font);
    
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, ui->widgetDials->height()*0.045));
    ui->labelTitle_2->setFont(font);
    ui->labelIN1->setFont(font);
    ui->labelIN2->setFont(font);
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, ui->widgetDials->height()*0.0322));
    ui->labelAUX->setFont(font);
    ui->labelBT->setFont(font);
    ui->labelOTG->setFont(font);
    ui->labelpb12->setFont(font);
    ui->labelpb34->setFont(font);
    ui->labelpb56->setFont(font);
    ui->labelpb78->setFont(font);
    ui->labelpb910->setFont(font);
}

void M62_PrivateWidget1::paintEvent(QPaintEvent* e){
    Q_UNUSED(e);
    static QSvgRenderer svg(QStringLiteral(":/Icon/duckingBackground.svg"));
    if (svg.isValid()) {
        QPainter painter(this);
        int shadowRadius = getConfig().shadowRadius;
        QRectF r = rect().adjusted(shadowRadius, shadowRadius, -shadowRadius, -shadowRadius);
        svg.render(&painter, r);
    }
}

bool M62_PrivateWidget1::eventFilter(QObject* obj, QEvent* e)
{
    if(e->type() == QEvent::Resize){
        setSizeFactor(mSizeFactor);
    }
    return QWidget::eventFilter(obj, e);
}

void M62_PrivateWidget1::setAllChildFont(QWidget* widget, const QFont& font)
{
    for (auto child : widget->children()) {
        QWidget *widget = qobject_cast<QWidget *>(child);
        if (widget) {
            widget->setFont(font);
            setAllChildFont(widget, font);
        }
    }
}

void M62_PrivateWidget1::in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value)
{
    WorkspaceObserver::setValue(attribute, value);
    updateAttribute();
    saveAttribute(attribute);
}

void M62_PrivateWidget1::setDialGeometry(QWidget *dial, QLabel *top, QLabel *left, QLabel *right)
{
    QWidget* parent = (QWidget*)dial->parent();
    int w = parent->width();
    int h = parent->height();
    float wPixelPerRatio= w / 100.0;
    float hPixelPerRatio = h / 100.0;
    int diameter = wPixelPerRatio * 90;

    QFont font = GLBFHandle.font();
    top->setGeometry(0, 0, w, hPixelPerRatio * 17.73);
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, top->height()));
    top->setFont(font);

    QRect rect(w/2-diameter/2, top->rect().bottom()+7*hPixelPerRatio, diameter, diameter);
    dial->setGeometry(rect);

    left->setGeometry(0, rect.bottom()*0.95, wPixelPerRatio*35, hPixelPerRatio * 13.93);
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, left->height()));
    left->setFont(font);

    right->setGeometry(wPixelPerRatio*50, rect.bottom()*0.95, wPixelPerRatio*50, hPixelPerRatio * 13.93);
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, right->height()));
    right->setFont(font);
}

void M62_PrivateWidget1::initConnect()
{
    connect(ui->buttonClose, &QPushButton::clicked, this, &FramelessWindow::close);
    connect(ui->input, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mWidgetListAll_attributeChanged(QString, QString, QString)));
    connect(ui->DialThreshold, &DialS1M5::valueChanged, this, [this](float value){
        in_mWidgetListAll_attributeChanged("", "Threshold", QString::number(value));
    });
    connect(ui->DialAttack, &DialS1M5::valueChanged, this, [this](float value){
        in_mWidgetListAll_attributeChanged("", "Attack", QString::number(value));
    });
    connect(ui->DialReduction, &DialS1M5::valueChanged, this, [this](float value){
        in_mWidgetListAll_attributeChanged("", "Reduction", QString::number(value));
    });
    connect(ui->DialRelease, &DialS1M5::valueChanged, this, [this](float value){
        in_mWidgetListAll_attributeChanged("", "Release", QString::number(value));
    });
    connect(ui->buttonSwitch, SIGNAL(toggled(bool)), this, SLOT(on_buttonOFF_toggled(bool)));
    connect(ui->IN1AUX, SIGNAL(toggled(bool)), this, SLOT(on_IN1AUX_toggled(bool)));
    connect(ui->IN2AUX, SIGNAL(toggled(bool)), this, SLOT(on_IN2AUX_toggled(bool)));
    connect(ui->IN1BT, SIGNAL(toggled(bool)), this, SLOT(on_IN1BT_toggled(bool)));
    connect(ui->IN2BT, SIGNAL(toggled(bool)), this, SLOT(on_IN2BT_toggled(bool)));
    connect(ui->IN1OTG, SIGNAL(toggled(bool)), this, SLOT(on_IN1OTG_toggled(bool)));
    connect(ui->IN2OTG, SIGNAL(toggled(bool)), this, SLOT(on_IN2OTG_toggled(bool)));
    connect(ui->playback1_2IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback1_2IN1_toggled(bool)));
    connect(ui->playback1_2IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback1_2IN2_toggled(bool)));
    connect(ui->playback3_4IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback3_4IN1_toggled(bool)));
    connect(ui->playback3_4IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback3_4IN2_toggled(bool)));
    connect(ui->playback5_6IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback5_6IN1_toggled(bool)));
    connect(ui->playback5_6IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback5_6IN2_toggled(bool)));
    connect(ui->playback7_8IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback7_8IN1_toggled(bool)));
    connect(ui->playback7_8IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback7_8IN2_toggled(bool)));
    connect(ui->playback9_10IN1, SIGNAL(toggled(bool)), this, SLOT(on_playback9_10IN1_toggled(bool)));
    connect(ui->playback9_10IN2, SIGNAL(toggled(bool)), this, SLOT(on_playback9_10IN2_toggled(bool)));
}

void M62_PrivateWidget1::on_buttonOFF_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "DuckingSwitch", checked?"true":"false");
}

void M62_PrivateWidget1::on_IN1AUX_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1AUX", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN2AUX_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2AUX", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN1BT_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1BT", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN2BT_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2BT", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN1OTG_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1OTGIN", checked?"true":"false");
}


void M62_PrivateWidget1::on_IN2OTG_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2OTGIN", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback1_2IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback1_2", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback1_2IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback1_2", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback3_4IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback3_4", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback3_4IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback3_4", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback5_6IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback5_6", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback5_6IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback5_6", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback7_8IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback7_8", checked?"true":"false");
}


void M62_PrivateWidget1::on_playback7_8IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback7_8", checked?"true":"false");
}

void M62_PrivateWidget1::on_playback9_10IN1_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN1Playback9_10", checked?"true":"false");
}

void M62_PrivateWidget1::on_playback9_10IN2_toggled(bool checked)
{
    in_mWidgetListAll_attributeChanged("", "IN2Playback9_10", checked?"true":"false");
}

void M62_PrivateWidget1::loadSettings()
{
    reset();
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", "IN 1+2");
        WorkspaceObserver::setValue("DuckingSwitch", false);
        WorkspaceObserver::setValue("Threshold", getThresholdDefaultValue());
        WorkspaceObserver::setValue("Attack", getAttackDefaultValue());
        WorkspaceObserver::setValue("Reduction", getReductionDefaultValue());
        WorkspaceObserver::setValue("Release", getReleaseDefaultValue());
        WorkspaceObserver::setValue("IN1AUX", true);
        WorkspaceObserver::setValue("IN2AUX", true);
        WorkspaceObserver::setValue("IN1BT", true);
        WorkspaceObserver::setValue("IN2BT", true);
        WorkspaceObserver::setValue("IN1OTGIN", true);
        WorkspaceObserver::setValue("IN2OTGIN", true);
        WorkspaceObserver::setValue("IN1Playback1_2", true);
        WorkspaceObserver::setValue("IN2Playback1_2", true);
        WorkspaceObserver::setValue("IN1Playback3_4", true);
        WorkspaceObserver::setValue("IN2Playback3_4", true);
        WorkspaceObserver::setValue("IN1Playback5_6", true);
        WorkspaceObserver::setValue("IN2Playback5_6", true);
        WorkspaceObserver::setValue("IN1Playback7_8", true);
        WorkspaceObserver::setValue("IN2Playback7_8", true);
        WorkspaceObserver::setValue("IN1Playback9_10", true);
        WorkspaceObserver::setValue("IN2Playback9_10", true);
    }
    setINChannelName(WorkspaceObserver::value("ChannelName").toString());
    setButtonOFFChecked(WorkspaceObserver::value("DuckingSwitch").toBool(), true, false);
    setThresholdValue(WorkspaceObserver::value("Threshold").toDouble(), false);
    setAttackValue(WorkspaceObserver::value("Attack").toDouble(), false);
    setReductionValue(WorkspaceObserver::value("Reduction").toDouble(), false);
    setReleaseValue(WorkspaceObserver::value("Release").toDouble(), false);
    setIN1AUXChecked(WorkspaceObserver::value("IN1AUX").toBool(), false);
    setIN2AUXChecked(WorkspaceObserver::value("IN2AUX").toBool(), false);
    setIN1BTChecked(WorkspaceObserver::value("IN1BT").toBool(), false);
    setIN2BTChecked(WorkspaceObserver::value("IN2BT").toBool(), false);
    setIN1OTGChecked(WorkspaceObserver::value("IN1OTGIN").toBool(), false);
    setIN2OTGChecked(WorkspaceObserver::value("IN2OTGIN").toBool(), false);
    setplayback1_2IN1Checked(WorkspaceObserver::value("IN1Playback1_2").toBool(), false);
    setplayback1_2IN2Checked(WorkspaceObserver::value("IN2Playback1_2").toBool(), false);
    setplayback3_4IN1Checked(WorkspaceObserver::value("IN1Playback3_4").toBool(), false);
    setplayback3_4IN2Checked(WorkspaceObserver::value("IN2Playback3_4").toBool(), false);
    setplayback5_6IN1Checked(WorkspaceObserver::value("IN1Playback5_6").toBool(), false);
    setplayback5_6IN2Checked(WorkspaceObserver::value("IN2Playback5_6").toBool(), false);
    setplayback7_8IN1Checked(WorkspaceObserver::value("IN1Playback7_8").toBool(), false);
    setplayback7_8IN2Checked(WorkspaceObserver::value("IN2Playback7_8").toBool(), false);
    setplayback9_10IN1Checked(WorkspaceObserver::value("IN1Playback9_10").toBool(), false);
    setplayback9_10IN2Checked(WorkspaceObserver::value("IN2Playback9_10").toBool(), false);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Threshold", QString::number(WorkspaceObserver::value("Threshold").toDouble()));
        emit attributeChanged(this->objectName(), "Save_Attack", QString::number(WorkspaceObserver::value("Attack").toDouble()));
        emit attributeChanged(this->objectName(), "Save_Reduction", QString::number(WorkspaceObserver::value("Reduction").toDouble()));
        emit attributeChanged(this->objectName(), "Save_Release", QString::number(WorkspaceObserver::value("Release").toDouble()));
        emit attributeChanged(this->objectName(), "Save_DuckingSwitch", QString::number(WorkspaceObserver::value("DuckingSwitch").toBool()));
        emit attributeChanged(this->objectName(), "Save_MapIN1", QString::number(calculateMapValue("IN1")));
        emit attributeChanged(this->objectName(), "Save_MapIN2", QString::number(calculateMapValue("IN2")));
    }
    updateAttribute();
}

void M62_PrivateWidget1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    if(attribute == "Language")
    {
        if(value == "English")
        {
            ui->DialThresholdT->setText("Threshold");
            ui->DialReductionT->setText("Reduction");
            ui->DialAttackT->setText("Attack");
            ui->DialReleaseT->setText("Release");
            ui->labelTitle_2->setText("Ducking Map");
            ui->labelIN1->setText("IN 1");
            ui->labelIN2->setText("IN 2");
            ui->labelAUX->setText("AUX");
            ui->labelBT->setText("BT");
            ui->labelOTG->setText("OTG");
            ui->labelpb12->setText("Playback 1/2");
            ui->labelpb34->setText("Playback 3/4");
            ui->labelpb56->setText("Playback 5/6");
            ui->labelpb78->setText("Playback 7/8");
            ui->labelpb910->setText("Playback 9/10");
        }
        else if(value == "Chinese")
        {
            ui->DialThresholdT->setText("闪避阈值");
            ui->DialReductionT->setText("闪避量");
            ui->DialAttackT->setText("启动时间");
            ui->DialReleaseT->setText("释放时间");
            ui->labelTitle_2->setText("闪避映射");
            ui->labelIN1->setText("IN 1");
            ui->labelIN2->setText("IN 2");
            ui->labelAUX->setText("AUX");
            ui->labelBT->setText("蓝牙");
            ui->labelOTG->setText("OTG");
            ui->labelpb12->setText("Playback 1/2");
            ui->labelpb34->setText("Playback 3/4");
            ui->labelpb56->setText("Playback 5/6");
            ui->labelpb78->setText("Playback 7/8");
            ui->labelpb910->setText("Playback 9/10");
        }
    }
}

void M62_PrivateWidget1::updateAttribute()
{
    if(mLineEditText != WorkspaceObserver::value("ChannelName").toString())
    {
        mLineEditText = WorkspaceObserver::value("ChannelName").toString();
    }
    if(mThreshold != WorkspaceObserver::value("Threshold").toDouble())
    {
        mThreshold = WorkspaceObserver::value("Threshold").toDouble();
        emit attributeChanged(this->objectName(), "Threshold", QString::number(mThreshold));
    }
    if(mAttack != WorkspaceObserver::value("Attack").toDouble())
    {
        mAttack = WorkspaceObserver::value("Attack").toDouble();
        emit attributeChanged(this->objectName(), "Attack", QString::number(mAttack));
    }
    if(mReduction != WorkspaceObserver::value("Reduction").toDouble())
    {
        mReduction = WorkspaceObserver::value("Reduction").toDouble();
        emit attributeChanged(this->objectName(), "Reduction", QString::number(mReduction));
    }
    if(mRelease != WorkspaceObserver::value("Release").toDouble())
    {
        mRelease = WorkspaceObserver::value("Release").toDouble();
        emit attributeChanged(this->objectName(), "Release", QString::number(mRelease));
    }

    uchar tempIn1 = calculateMapValue("IN1");
    uchar tempIn2 = calculateMapValue("IN2");
    bool isChanged1 = false;
    bool isChanged2 = false;
    if(mOFF != static_cast<int>(WorkspaceObserver::value("DuckingSwitch").toBool()))
    {
        isChanged1 = true;
        isChanged2 = true;
        mOFF = WorkspaceObserver::value("DuckingSwitch").toBool();
        emit attributeChanged(this->objectName(), "DuckingSwitch", QString::number(mOFF));
    }
    if(tempIn1 != mIn1)
    {
        isChanged1 = true;
        mIn1 = tempIn1;
        emit attributeChanged(this->objectName(), "MapIN1", QString::number(mIn1));
    }
    if(tempIn2 != mIn2)
    {
        isChanged2 = true;
        mIn2 = tempIn2;
        emit attributeChanged(this->objectName(), "MapIN2", QString::number(mIn2));
    }
    if(isChanged1){
        sendSigWorkState(1);
    }
    if(isChanged2){
        sendSigWorkState(2);
    }
}

void M62_PrivateWidget1::saveAttribute(QString attribute, QString value)
{
    if(!value.isEmpty())
    {
        WorkspaceObserver::setValue(attribute, value);
    }

    auto saveValue = [this](const QString& attribute, const QString& value){
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), QString("Save_")+attribute, value);
        }
    };

    if(attribute == "Threshold")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("Threshold").toDouble()));
    }
    else if(attribute == "Attack")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("Attack").toDouble()));
    }
    else if(attribute == "Reduction")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("Reduction").toDouble()));
    }
    else if(attribute == "Release")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("Release").toDouble()));
    }
    else if(attribute == "DuckingSwitch")
    {
        saveValue(attribute, QString::number(WorkspaceObserver::value("DuckingSwitch").toBool()));
    }
    else if(attribute.contains("IN1")){
        saveValue("MapIN1", QString::number(calculateMapValue("IN1")));
    }
    else if(attribute.contains("IN2")){
        saveValue("MapIN2", QString::number(calculateMapValue("IN2")));
    }
}

void M62_PrivateWidget1::reset()
{
    mThreshold = DBL_MAX;
    mAttack = DBL_MAX;
    mReduction = DBL_MAX;
    mRelease = DBL_MAX;
    mOFF = INT_MAX;
    mIN1AUX = INT_MAX;
    mIN2AUX = INT_MAX;
    mIN1BT = INT_MAX;
    mIN2BT = INT_MAX;
    mIN1OTGIN = INT_MAX;
    mIN2OTGIN = INT_MAX;
    mIN1Playback1_2 = INT_MAX;
    mIN2Playback1_2 = INT_MAX;
    mIN1Playback3_4 = INT_MAX;
    mIN2Playback3_4 = INT_MAX;
    mIN1Playback5_6 = INT_MAX;
    mIN2Playback5_6 = INT_MAX;
    mIN1Playback7_8 = INT_MAX;
    mIN2Playback7_8 = INT_MAX;
    mIN1Playback9_10 = INT_MAX;
    mIN2Playback9_10 = INT_MAX;
    mIn1 = INT_MAX;
    mIn2 = INT_MAX;
}

uchar M62_PrivateWidget1::calculateMapValue(const QString& prefix)
{
    uchar result = 0;
    result = (result & ~(1 << 0)) | ((uchar)WorkspaceObserver::value(prefix + "AUX").toBool() << 0);
    result = (result & ~(1 << 1)) | ((uchar)WorkspaceObserver::value(prefix + "BT").toBool() << 1);
    result = (result & ~(1 << 2)) | ((uchar)WorkspaceObserver::value(prefix + "OTGIN").toBool() << 2);
    result = (result & ~(1 << 3)) | ((uchar)WorkspaceObserver::value(prefix + "Playback1_2").toBool() << 3);
    result = (result & ~(1 << 4)) | ((uchar)WorkspaceObserver::value(prefix + "Playback3_4").toBool() << 4);
    result = (result & ~(1 << 5)) | ((uchar)WorkspaceObserver::value(prefix + "Playback5_6").toBool() << 5);
    result = (result & ~(1 << 6)) | ((uchar)WorkspaceObserver::value(prefix + "Playback7_8").toBool() << 6);
    result = (result & ~(1 << 7)) | ((uchar)WorkspaceObserver::value(prefix + "Playback9_10").toBool() << 7);
    return result;
}

void M62_PrivateWidget1::sendSigWorkState(int index)
{
    emit attributeChanged(this->objectName(), index==1?"IN1WorkState":"IN2WorkState", mOFF==1?(index==1?(mIn1==0?"0":"1"):(mIn2==0?"0":"1")):"0");
}

void M62_PrivateWidget1::handleMoving(const QPointF& delta)
{
    if(ui->widgetTop->underMouse())
        FramelessWindow::handleMoving(delta);
}
