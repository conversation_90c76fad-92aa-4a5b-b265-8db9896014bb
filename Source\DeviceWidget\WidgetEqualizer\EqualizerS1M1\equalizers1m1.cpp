#include "equalizers1m1.h"
#include "framelesswindow.h"
#include "ui_equalizers1m1.h"
#include <ComboBoxS1M3.h>
#include "globalfont.h"
#include "appsettings.h"
#include <QSvgRenderer>

EqualizerS1M1::EqualizerS1M1(QWidget *parent)
    : EqualizerBase(parent)
    , ui(new Ui::EqualizerS1M1)
{
    mWidget = new QWidget(this);
    mWidget->setObjectName("mWidget");
    ui->setupUi(mWidget);
    setCentralWidget(mWidget);
    setWindowMode(FramelessWindow::WindowMode::Custom);
    setShadowRadius(5);
    ui->horizontalLayout->setAlignment(Qt::AlignTop);
    QString style;
    style = "QWidget {"
            "   border-radius: 8px;"
            "}QWidget{background-color: #161616;}";
    ui->widgetEqPanel->setStyleSheet(style);
    ui->widgetLeft->setStyleSheet(style);
    ui->widgetRight->setStyleSheet(style);
    ui->buttonClose->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/close.svg); }");
    ui->buttonSwitch->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/autoStartUnchecked.svg); }QPushButton:checked { image: url(:/Icon/autoStart.svg); }");
    ui->labelTitle->setStyleSheet("color:rgb(222, 222, 222);");
    ui->label->setStyleSheet("color:rgb(255, 255, 255);border-bottom: 1px solid rgb(31,31,31);border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;");
    ui->label_2->setStyleSheet("color:rgb(255, 255, 255);border-bottom: 1px solid rgb(31,31,31);border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;");
    mHashLanguages.insert(ui->labelTitle, {{"English", "EQ"}, {"Chinese", "均衡器"}});
    mHashLanguages.insert(ui->label, {{"English", "INPUT"}, {"Chinese", "输入"}});
    mHashLanguages.insert(ui->label_2, {{"English", "OUTPUT"}, {"Chinese", "输出"}});
    ui->slider->setRange(0, -10, -52, -60);
    ui->slider->setDefault(-38);
    ui->slider->setHeightRatio(5, 4, 89);
    ui->slider_2->setRange(9, -10, -52, -90);
    ui->slider_2->setDefault(-38);
    ui->slider_2->setHeightRatio(5, 4, 89);
    ui->widgetMeter->setScaleLineHidden(true);
    ui->widgetMeter2->setScaleLineHidden(true);
    ui->widgetMeter->setHeightRatio(2, 1, 95, 2);
    ui->widgetMeter->setWidthRatio(35, 15, 15, 10, 15);
    ui->widgetMeter2->setHeightRatio(2, 1, 95, 2);
    ui->widgetMeter2->setWidthRatio(35, 15, 15, 10, 15);

    connect(ui->widgetEqPanel, &EqualizerPanelS1M1::attributeChanged, this, &EqualizerS1M1::attributeChanged);
    connect(ui->buttonSwitch, &QPushButton::clicked, this, [this](bool checked){
        emit attributeChanged(this->objectName(), "Switch", QString::number(checked));
    });
    connect(ui->buttonClose, &QPushButton::clicked, this, &FramelessWindow::close);
    connect(&APPSHandle, &AppSettingsSubject::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "ModifyLanguage"){
            for(auto element : mHashLanguages.keys()){
                if(auto it = qobject_cast<QLabel*>(element))
                    it->setText(mHashLanguages.value(element).value(value));
            }
            ui->widgetEqPanel->setLanguage(value);
        }
    });
    connect(ui->slider, &VSliderS1M2::valueChanged, this, [this](int value){
        ui->widgetEqPanel->modifyPreGain(value);
        emit attributeChanged(this->objectName(), "GainInputLeft", QString::number(value));
        emit attributeChanged(this->objectName(), "GainInputRight", QString::number(value));
    });
    connect(ui->slider_2, &VSliderS1M2::valueChanged, this, [this](int value){
        emit attributeChanged(this->objectName(), "GainOutputLeft", QString::number(value));
        emit attributeChanged(this->objectName(), "GainOutputRight", QString::number(value));
    });
}

EqualizerS1M1::~EqualizerS1M1()
{
    delete ui;
}

EqualizerS1M1& EqualizerS1M1::setName(const QString& name){
    setObjectName(name);
    ui->widgetEqPanel->setName(name);
    return *this;
}

EqualizerS1M1& EqualizerS1M1::setFont(const QFont& font){
    mFont = font;
    ui->widgetEqPanel->setFont(font);
    return *this;
}

void EqualizerS1M1::setSizeFactor(double sizeFactor){
    int lrMargin = 0.04*mWidget->width();
    int topMargin = 0*mWidget->height();
    int bottomMargin = 0.05*mWidget->width();
    int spacing = 0.0161*mWidget->width();
    int lrWidgetWidth = 0.161*mWidget->width();
    int topWidgetHeight = 0.11*mWidget->height();
    ui->horizontalLayout_2->setContentsMargins(lrMargin, topMargin, lrMargin, bottomMargin);
    ui->horizontalLayout_2->setSpacing(spacing);
    ui->widgetLeft->setFixedWidth(lrWidgetWidth);
    ui->widgetRight->setFixedWidth(lrWidgetWidth);
    ui->widgetTop->setFixedHeight(topWidgetHeight);
    ui->buttonSwitch->setFixedSize(topWidgetHeight * 0.4, topWidgetHeight * 0.4);
    ui->buttonClose->setFixedSize(topWidgetHeight * 0.35, topWidgetHeight * 0.35);
    ui->widgetTop->layout()->setContentsMargins(spacing, 0.009 * mWidget->width(), spacing, 0);
    ui->widgetTop->layout()->activate();
    ui->gridLayout_6->setContentsMargins(ui->widgetLeft->width()*0.08, ui->widgetLeft->height()*0.026, 0, ui->widgetLeft->height()*0.05);
    ui->gridLayout_6->setSpacing(ui->widgetLeft->width()*0.05);
    ui->gridLayout_7->setContentsMargins(ui->widgetRight->width()*0.08, ui->widgetRight->height()*0.026, 0, ui->widgetRight->height()*0.05);
    ui->gridLayout_7->setSpacing(ui->widgetRight->width()*0.05);
    ui->label->setFixedHeight(ui->widgetLeft->height()*0.0863);
    ui->widgetMeter->setFixedWidth(ui->widgetLeft->width()*0.4);
    ui->widgetMeter->setFont(mFont);
    ui->label_2->setFixedHeight(ui->widgetRight->height()*0.0863);
    ui->widgetMeter2->setFixedWidth(ui->widgetRight->width()*0.4);
    ui->widgetMeter2->setFont(mFont);
    ui->slider->setFixedWidth(ui->widgetLeft->width()*0.25);
    ui->slider_2->setFixedWidth(ui->widgetRight->width()*0.25);
    ui->labelTitle->setFixedHeight(topWidgetHeight * 0.5);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelTitle->height()*0.9));
    ui->labelTitle->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->label->height()*0.5));
    ui->label->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->label_2->height()*0.5));
    ui->label_2->setFont(mFont);
    ui->slider->setFont(mFont);
    ui->slider_2->setFont(mFont);
    ui->horizontalLayout_2->activate();
    ui->widgetEqPanel->setSizeFactor(sizeFactor);
}

void EqualizerS1M1::showEvent(QShowEvent* e){
#ifdef Q_OS_MACOS
    setSizeFactor(mScaleRatio);
    QApplication::processEvents();
#endif
}

void EqualizerS1M1::paintEvent(QPaintEvent* e){
    Q_UNUSED(e);
    static QSvgRenderer svg(QStringLiteral(":/Icon/EqBackgroundOut.svg"));
    if (svg.isValid()) {
        QPainter painter(this);
        int shadowRadius = getConfig().shadowRadius;
        QRectF r = rect().adjusted(shadowRadius, shadowRadius, -shadowRadius, -shadowRadius);
        svg.render(&painter, r);
    }
}

void EqualizerS1M1::handleMoving(const QPointF& delta){
    if(ui->widgetTop->underMouse())
        FramelessWindow::handleMoving(delta);
}

void EqualizerS1M1::setEqualizerBands(int count){
    ui->widgetEqPanel->setEqualizerBands(count);
}

void EqualizerS1M1::setEqualizerData(const QString& band, const QString& property, const QString& data){
    ui->widgetEqPanel->setEqualizerData(band, property, data);
}

void EqualizerS1M1::setScaleFactor(double sizeFactor){
    int shadowRadius = 2*getConfig().shadowRadius;
    static QSize baseSize(522+shadowRadius,425+shadowRadius);
    setMinimumSize(baseSize.width() * sizeFactor, baseSize.height() * sizeFactor);
    resize(minimumSize().width(), minimumSize().height());
#ifdef Q_OS_WIN
    if(!isVisible()){
        FramelessWindow::show();
        hide();
    }
#elif defined(Q_OS_MACOS)
    if(isVisible()){
#endif
        setSizeFactor(sizeFactor);
#ifdef Q_OS_MACOS
    }
#endif
    mScaleRatio = sizeFactor;
}

void EqualizerS1M1::setStateTarget(bool state)
{
    ui->widgetEqPanel->setTargetChecked(state);
}

void EqualizerS1M1::setStateSource(bool state)
{
    ui->widgetEqPanel->setSourceFRChecked(state);
}

void EqualizerS1M1::setStateEachFilter(bool state)
{
    ui->widgetEqPanel->setEachFilterChecked(state);
}

void EqualizerS1M1::setStateCombined(bool state)
{
    ui->widgetEqPanel->setCombinedFilterChecked(state);
}

void EqualizerS1M1::setStateFiltered(bool state)
{
    ui->widgetEqPanel->setFilteredFRChecked(state);
}

void EqualizerS1M1::setFilteredType(int type)
{
    ui->widgetEqPanel->setCompensatedChecked(type);
}

void EqualizerS1M1::setTargetFile(QString path)
{
    ui->widgetEqPanel->importTarget(path);
}

void EqualizerS1M1::setSourceFile(QString path)
{
    ui->widgetEqPanel->importSource(path);
}

void EqualizerS1M1::setStateSwitch(bool is, bool isSendSig)
{
    ui->buttonSwitch->setChecked(is);
    if(isSendSig)
        emit attributeChanged(this->objectName(), "Switch", QString::number(is));
    ui->widgetEqPanel->updaEachBandChart();
}

void EqualizerS1M1::setGainInputLeft(float value)
{
    ui->slider->setValue(value);
}

void EqualizerS1M1::setGainInputRight(float value)
{
    ui->slider->setValue(value);
}

void EqualizerS1M1::setGainOutputLeft(float value)
{
    ui->slider_2->setValue(value);
}

void EqualizerS1M1::setGainOutputRight(float value)
{
    ui->slider_2->setValue(value);
}

void EqualizerS1M1::setVolumeMeterInputLeft(int value)
{
    ui->widgetMeter->setValueGained(value);
}

void EqualizerS1M1::setVolumeMeterInputRight(int value)
{
    ui->widgetMeter->setValueOrigin(value);
}

void EqualizerS1M1::setVolumeMeterOutputLeft(int value)
{
    ui->widgetMeter2->setValueGained(value);
}

void EqualizerS1M1::setVolumeMeterOutputRight(int value)
{
    ui->widgetMeter2->setValueOrigin(value);
}
